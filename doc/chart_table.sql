-- 图表数据表
CREATE TABLE `chart` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type` int NOT NULL COMMENT '图表类型',
  `time` bigint NOT NULL COMMENT '时间戳（秒）',
  `value` decimal(25,8) NOT NULL COMMENT '数值',
  `data` text COMMENT 'JSON数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_time` (`time`),
  KEY `idx_type_time` (`type`, `time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图表数据表';

-- 如果表已存在，需要修改字段类型
-- ALTER TABLE chart MODIFY COLUMN `time` bigint NOT NULL COMMENT '时间戳（秒）';