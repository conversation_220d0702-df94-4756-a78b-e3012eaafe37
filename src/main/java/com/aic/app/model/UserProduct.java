package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class UserProduct {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String orderNo;
    private Long userId;
    private Integer type;
    private Long productId;
    /**
     * 数量
     */
    private BigDecimal quantity;
    private BigDecimal price;
    /**
     * 算力
     */
    private BigDecimal power;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 收益
     */
    private BigDecimal profit;
    /**
     *     @Schema(description = "状态：0-待发货，1-待收货，2-已完成")
     */
    private Integer status;
    private Integer day;
    private Integer releaseDay;
    private BigDecimal rate;
    private BigDecimal fee;
    // private BigDecimal feeToken;
    // private BigDecimal price1;
    // private BigDecimal price2;
    // private int source;

    /**
     * 理财时间
     */
    private Date createTime;

    /**
     * 每日释放金额（普通商品专用）
     */
    private BigDecimal dailyReleaseAmount;

    /**
     * 已释放总金额（普通商品专用）
     */
    private BigDecimal totalReleased;

    /**
     * 可提取金额，已释放未提取（普通商品专用）
     */
    private BigDecimal availableAmount;

    /**
     * 最后释放日期（普通商品专用）
     */
    private Date lastReleaseDate;

    /**
     * 邀请码
     */
    private transient String code;
    private transient String address;
    private transient String productName;


    public UserProduct(Long userId, String orderNo, Product product, BigDecimal power) {
        this.userId = userId;
        this.orderNo = orderNo;
        this.productId = product.getId();
        this.type = product.getType();
        this.power = power;
        // 默认待确认
        this.status = 1;
        this.day = product.getDay();
        this.releaseDay = 0;
        this.rate = product.getRate();
        this.createTime = new Date();

        // 初始化普通商品相关字段
        this.dailyReleaseAmount = BigDecimal.ZERO;
        this.totalReleased = BigDecimal.ZERO;
        this.availableAmount = BigDecimal.ZERO;
        this.lastReleaseDate = null;
    }

}
