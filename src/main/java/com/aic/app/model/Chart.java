package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 图表数据表
 */
@Data
@NoArgsConstructor
@TableName("chart")
public class Chart implements Serializable {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 图表类型
     */
    private Integer type;
    
    /**
     * 时间戳（秒）
     */
    private Long time;
    
    /**
     * 数值
     */
    private BigDecimal value;
    
    /**
     * JSON数据
     */
    private String data;
    
    /**
     * 创建时间
     */
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    
    public Chart(Integer type, Long time, BigDecimal value) {
        this.type = type;
        this.time = time;
        this.value = value;
        this.createTime = new Date();
    }
    
    public Chart(Integer type, Long time, BigDecimal value, String data) {
        this.type = type;
        this.time = time;
        this.value = value;
        this.data = data;
        this.createTime = new Date();
    }
}