package com.aic.app.mapper;

import com.aic.app.model.Chart;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 图表数据Mapper
 */
@Mapper
public interface ChartMapper extends BaseMapper<Chart> {
    
    /**
     * 根据类型查询图表数据
     */
    @Select("SELECT * FROM chart WHERE type = #{type} ORDER BY time DESC")
    List<Chart> getByType(@Param("type") Integer type);
    
    /**
     * 根据类型和时间范围查询图表数据
     */
    @Select("SELECT * FROM chart WHERE type = #{type} AND time BETWEEN #{startTime} AND #{endTime} ORDER BY time ASC")
    List<Chart> getByTypeAndTimeRange(@Param("type") Integer type, 
                                     @Param("startTime") Long startTime, 
                                     @Param("endTime") Long endTime);
    
    /**
     * 获取指定类型的最新数据
     */
    @Select("SELECT * FROM chart WHERE type = #{type} ORDER BY time DESC LIMIT 1")
    Chart getLatestByType(@Param("type") Integer type);
    
    /**
     * 获取指定类型的数据总数
     */
    @Select("SELECT COUNT(*) FROM chart WHERE type = #{type}")
    Long getCountByType(@Param("type") Integer type);
    
    /**
     * 获取指定类型在指定时间范围内的平均值
     */
    @Select("SELECT AVG(value) FROM chart WHERE type = #{type} AND time BETWEEN #{startTime} AND #{endTime}")
    BigDecimal getAvgValueByTypeAndTimeRange(@Param("type") Integer type, 
                                           @Param("startTime") Long startTime, 
                                           @Param("endTime") Long endTime);
    
    /**
     * 获取指定类型在指定时间范围内的最大值
     */
    @Select("SELECT MAX(value) FROM chart WHERE type = #{type} AND time BETWEEN #{startTime} AND #{endTime}")
    BigDecimal getMaxValueByTypeAndTimeRange(@Param("type") Integer type, 
                                           @Param("startTime") Long startTime, 
                                           @Param("endTime") Long endTime);
    
    /**
     * 获取指定类型在指定时间范围内的最小值
     */
    @Select("SELECT MIN(value) FROM chart WHERE type = #{type} AND time BETWEEN #{startTime} AND #{endTime}")
    BigDecimal getMinValueByTypeAndTimeRange(@Param("type") Integer type, 
                                           @Param("startTime") Long startTime, 
                                           @Param("endTime") Long endTime);
}