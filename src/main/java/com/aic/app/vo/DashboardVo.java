package com.aic.app.vo;

import com.aic.app.model.Chart;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;

/**
 * Dashboard数据VO
 */
@Data
@NoArgsConstructor
@Schema(description = "Dashboard数据")
public class DashboardVo {

    /**
     * 全网质押数量
     */
    @Schema(description = "全网质押数量")
    private BigDecimal totalStakeAmount;

    /**
     * 全网未质押数量
     */
    @Schema(description = "全网未质押数量")
    private BigDecimal totalUnstakedAmount;

    /**
     * LP价值
     */
    @Schema(description = "LP价值")
    private BigDecimal lpValue;

    /**
     * 债券总售出金额
     */
    @Schema(description = "债券总售出金额")
    private BigDecimal totalBondsSold;

    /**
     * 总市值
     */
    @Schema(description = "总市值")
    private BigDecimal totalMarketCap;

    /**
     * 总铸造
     */
    @Schema(description = "总铸造")
    private BigDecimal totalSupply;

    /**
     * XYC价格
     */
    @Schema(description = "XYC价格")
    private BigDecimal xycPrice;

    /**
     * 图表数据1
     */
    @Schema(description = "图表数据1")
    private List<ChartData> chartData1;

    /**
     * 图表数据2
     */
    @Schema(description = "图表数据2")
    private List<ChartData> chartData2;

    /**
     * 图表数据3
     */
    @Schema(description = "图表数据3")
    private List<ChartData> chartData3;

    /**
     * 图表数据内部类
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "图表数据")
    public static class ChartData {

        /**
         * 时间戳（秒）
         */
        @Schema(description = "时间戳（秒）")
        private Long time;

        /**
         * 数值
         */
        @Schema(description = "数值")
        private BigDecimal value;

        /**
         * JSON数据
         */
        @Schema(description = "JSON数据")
        private String data;

        public ChartData(Long time, BigDecimal value) {
            this.time = time;
            this.value = value;
        }

        public ChartData(Chart chart) {
            this.time = chart.getTime();
            this.value = chart.getValue();
            this.data = chart.getData();
        }
    }

    /**
     * 创建默认数据（全部为0）
     */
    public static DashboardVo createDefault() {
        DashboardVo vo = new DashboardVo();
        vo.totalStakeAmount = BigDecimal.ZERO;
        vo.totalUnstakedAmount = BigDecimal.ZERO;
        vo.lpValue = BigDecimal.ZERO;
        vo.totalBondsSold = BigDecimal.ZERO;
        vo.totalMarketCap = BigDecimal.ZERO;
        vo.totalSupply = BigDecimal.ZERO;
        vo.xycPrice = BigDecimal.ZERO;
        vo.chartData1 = new ArrayList<>();
        vo.chartData2 = new ArrayList<>();
        vo.chartData3 = new ArrayList<>();
        return vo;
    }
}
