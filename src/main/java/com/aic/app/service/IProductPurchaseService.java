package com.aic.app.service;

import com.aic.app.model.Product;
import com.aic.app.model.User;
import com.aic.app.model.UserProduct;

import java.math.BigDecimal;

/**
 * 产品购买服务接口
 */
public interface IProductPurchaseService {
    
    /**
     * 购买产品
     */
    UserProduct purchaseProduct(User user, Product product, BigDecimal amount, BigDecimal quantity);
    
    /**
     * 处理城主商品购买
     */
    void processCityLordProduct(UserProduct userProduct, Product product, BigDecimal amount);
    
    /**
     * 通用购买后处理
     */
    void afterProductPurchase(UserProduct userProduct, Product product);
    
    /**
     * 更新产品已售数量
     */
    void updateProductSoldQuantity(Long productId, BigDecimal quantity);
    
    /**
     * 增加用户4倍收益次数
     */
    void addUserQuadrupleRewardTimes(Long userId, int times);
}