package com.aic.app.service.impl;

import com.aic.app.mapper.ChartMapper;
import com.aic.app.model.Chart;
import com.aic.app.service.IChartService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 图表数据服务实现
 */
@Service
@Slf4j
public class ChartServiceImpl extends ServiceImpl<ChartMapper, Chart> implements IChartService {
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addChartData(Integer type, Long time, BigDecimal value) {
        Chart chart = new Chart(type, time, value);
        boolean result = this.save(chart);
        if (result) {
            log.info("添加图表数据成功，类型: {}, 时间戳: {}, 数值: {}", type, time, value.stripTrailingZeros().toPlainString());
        } else {
            log.error("添加图表数据失败，类型: {}, 时间戳: {}, 数值: {}", type, time, value.stripTrailingZeros().toPlainString());
        }
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addChartData(Integer type, Long time, BigDecimal value, String data) {
        Chart chart = new Chart(type, time, value, data);
        boolean result = this.save(chart);
        if (result) {
            log.info("添加图表数据成功，类型: {}, 时间戳: {}, 数值: {}, JSON数据长度: {}", 
                    type, time, value.stripTrailingZeros().toPlainString(), data != null ? data.length() : 0);
        } else {
            log.error("添加图表数据失败，类型: {}, 时间戳: {}, 数值: {}", type, time, value.stripTrailingZeros().toPlainString());
        }
        return result;
    }
}