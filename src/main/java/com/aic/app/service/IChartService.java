package com.aic.app.service;

import com.aic.app.model.Chart;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
 * 图表数据服务接口
 */
public interface IChartService extends IService<Chart> {
    
    /**
     * 添加图表数据
     */
    boolean addChartData(Integer type, Long time, BigDecimal value);
    
    /**
     * 添加图表数据（包含JSON数据）
     */
    boolean addChartData(Integer type, Long time, BigDecimal value, String data);
}