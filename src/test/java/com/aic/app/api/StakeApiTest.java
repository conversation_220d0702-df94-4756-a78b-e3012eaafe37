package com.aic.app.api;

import com.aic.app.vo.HomeDataVo;
import com.aic.app.vo.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
public class StakeApiTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private String testToken;

    @BeforeEach
    void setUp() {
        // 设置Redis测试数据
        stringRedisTemplate.opsForHash().putAll("xyc:stats", Map.of(
            "totalStakeAmount", "1000000.00000000",
            "currentIndex", "1.50000000",
            "stakePrincipal", "800000.00000000",
            "totalInterest", "200000.00000000",
            "apy", "0.15000000"
        ));

        // 创建测试token
        testToken = UUID.randomUUID().toString();
        stringRedisTemplate.opsForValue().set("xyc:user:token:" + testToken,
            "100001", Duration.ofHours(2));
    }

    @Test
    public void testStakeApi() throws Exception {
        MvcResult result = mockMvc.perform(get("/api/stake"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("success"))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.totalStakeAmount").value(1000000))
                .andExpect(jsonPath("$.data.currentIndex").value(1.5))
                .andExpect(jsonPath("$.data.stakePrincipal").value(800000))
                .andExpect(jsonPath("$.data.totalInterest").value(200000))
                .andExpect(jsonPath("$.data.myStakeAmount").value(0))
                .andExpect(jsonPath("$.data.pendingAmount").value(0))
                .andExpect(jsonPath("$.data.stakeStaticPool").value(0))
                .andExpect(jsonPath("$.data.stakeDynamicPool").value(0))
                .andExpect(jsonPath("$.data.lockStaticPool").value(0))
                .andExpect(jsonPath("$.data.lockDynamicPool").value(0))
                .andExpect(jsonPath("$.data.apy").value(0))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("质押接口响应: " + responseContent);

        // 验证返回的数据结构
        Result<HomeDataVo> response = objectMapper.readValue(responseContent, 
            objectMapper.getTypeFactory().constructParametricType(Result.class, HomeDataVo.class));
        
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("success", response.getMsg());
        
        HomeDataVo data = response.getData();
        assertNotNull(data);
    }



}
