package com.aic.app.api;

import com.aic.app.config.InterceptorConfig;
import com.aic.app.job.BaseTest;

import jakarta.annotation.Resource;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.servlet.MockMvcBuilder;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
@Import(InterceptorConfig.class)
public class ApiTest extends BaseTest {

    @Resource
    IndexController indexController;
    @Resource
    LoginInterceptor loginInterceptor;
    @Resource
    UserController userController;

    @BeforeEach
    void setUp() {
        // mockMvc.getDispatcherServlet().getWebApplicationContext().getBean(LoginInterceptor.class);
        mockMvc = MockMvcBuilders.standaloneSetup(userController, indexController).addInterceptors(loginInterceptor).build();
    }
    
    // get sign msg
    @Test
    public void signMsg() throws Exception {
        this.mockMvc.perform(get("/api/sign-msg")
                        .param("address", "0xB5686Faa50e064917593AD753C0C26B7764fDD85"))
                .andExpect(status().isOk())
                .andDo(print());
    }
    
    @Test
    public void userinfo() throws Exception {
        login(1);
        this.mockMvc.perform(get("/api/userinfo")
                        .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print());
    }
    
    // test /api/product
    @Test
    public void product() throws Exception {
        this.mockMvc.perform(get("/api/product?page=1&size=20&types=1,2"))
                .andExpect(status().isOk())
                .andDo(print());
    }

    // test /api/user/purchase-records
    @Test
    public void purchaseRecords() throws Exception {
        login(1);
        this.mockMvc.perform(get("/api/user/purchase-records?page=1&size=10")
                        .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print());
    }

    // test /api/user/purchase-records without login
    @Test
    public void purchaseRecordsWithoutLogin() throws Exception {
        login(1);
        this.mockMvc.perform(get("/api/user/purchase-records?page=1&size=10")
                .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print());
    }

    // test invite-records
    @Test
    public void inviteRecordsWithoutLogin() throws Exception {
        login(1);
        this.mockMvc.perform(get("/api/user/invite-records?page=1&size=10")
                .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print());
    }

    // test /stake
    @Test
    public void test_stake() throws Exception {
        this.mockMvc.perform(get("/api/stake"))
                .andExpect(status().isOk())
                .andDo(print());
    }

    // test /api/bonds
    @Test
    public void test_bonds() throws Exception {
        this.mockMvc.perform(get("/api/bonds"))
                .andExpect(status().isOk())
                .andDo(print());
    }


    // test /dashboard
    @Test
    public void test_dashboard() throws Exception {
        this.mockMvc.perform(get("/api/dashboard"))
                .andExpect(status().isOk())
                .andDo(print());
    }
    

}
